import React from 'react';
import { View } from 'react-native';
import { Crown } from 'lucide-react-native';
import { useAppTheme } from '@/hooks/useAppTheme';
import { createStyles } from './styles';

interface PremiumBadgeProps {
  visible: boolean;
}

export function PremiumBadge({ visible }: PremiumBadgeProps) {
  const theme = useAppTheme();
  const styles = createStyles(theme);

  if (!visible) return null;

  return (
    <View style={styles.premiumBadge}>
      <Crown size={10} color="#000" />
    </View>
  );
}
