import { StyleSheet } from 'react-native';
import { AppTheme } from '@/lib/theme/themes';

export const createStyles = (theme: AppTheme) => StyleSheet.create({
  tabsContainer: {
    flexDirection: 'row',
    marginTop: theme.spacing.lg, 
    marginBottom: theme.spacing.md,
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderBottomColor: theme.colors.border, 
  },
  tab: {
    marginRight: theme.spacing.lg, // Spacing between tabs
    paddingBottom: theme.spacing.sm, // Padding below text for border
    borderBottomWidth: 2,
    borderBottomColor: 'transparent', // Default transparent
  },
  activeTab: {
    // borderBottomColor set dynamically
  },
  tabText: {
    fontFamily: theme.fonts.medium,
    fontSize: 16,
    // Color set dynamically
  },
}); 