import { StyleSheet } from 'react-native';
import { AppTheme } from '@/lib/theme/themes';

export const createStyles = (theme: AppTheme) =>
  StyleSheet.create({
    container: {
      paddingHorizontal: theme.spacing.md,
      paddingVertical: theme.spacing.sm,
      borderRadius: theme.radius.md,
      flexDirection: 'row',
      alignItems: 'center',
      minWidth: 80, // Keep minWidth or adjust as needed
      // Consider adding some default margin if used in a list
      marginRight: theme.spacing.sm,
      marginBottom: theme.spacing.sm,
    },
    themeName: {
      color: '#FFFFFF', // Hardcoded white - potential readability issue
      fontFamily: theme.fonts.medium,
      fontSize: theme.fontSizes.sm,
      marginLeft: theme.spacing.xs,
    },
  });
