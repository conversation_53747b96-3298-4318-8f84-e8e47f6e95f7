import React from 'react';
import { View, Text, ScrollView, TouchableOpacity } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useAppTheme } from '@/hooks/useAppTheme';
import { useTranslation } from 'react-i18next';
import { mockThemes } from '@/utils/mockData';
import SearchBar from '@/components/ui/SearchBar';
import HeaderBar from '@/components/ui/HeaderBar';
import { BarChart2 } from 'lucide-react-native';
import { createStyles } from './HomeScreen.styles';
import { useRouter } from 'expo-router';

// Import feature components
import { ThemeCarousel } from '@/features/home/<USER>/ThemeCarousel';
import { HomeScreenLoading } from '@/features/home/<USER>/HomeScreenLoading';
import { HomeScreenError } from '@/features/home/<USER>/HomeScreenError';
import { HomeScreenContent } from '@/features/home/<USER>/HomeScreenContent';
import { useHomeScreenData } from '@/features/home/<USER>/useHomeScreenData';

export default function HomeScreen() {
  const theme = useAppTheme();
  const styles = createStyles(theme);
  const { t } = useTranslation();
  const router = useRouter();
  const [searchQuery, setSearchQuery] = React.useState('');

  const {
    storyListTabs,
    activeStoryListTab,
    setActiveStoryListTab,
    stories,
    featuredStory,
    isLoading,
    error,
    fetchStories,
    mapTabToFilter,
  } = useHomeScreenData();

  const themes = mockThemes;

  const handleStoryPress = (storyId: string) => {
    console.log('Story pressed:', storyId);
    router.push(`/stories/${storyId}`);
  };

  const handleThemePress = (themeId: string) => {
    console.log('Theme pressed:', themeId);
    // 导航到主题搜索结果
    router.push(`/search?topic=${encodeURIComponent(themeId)}`);
  };

  const handleSearch = (text: string) => {
    if (text.trim()) {
      // 导航到搜索结果页面
      router.push(`/search?q=${encodeURIComponent(text)}`);
    }
  };

  const renderContent = () => {
    if (isLoading && stories.length === 0) {
      return <HomeScreenLoading />;
    }

    if (error) {
      return (
        <HomeScreenError
          error={error}
          onRetry={() => fetchStories(mapTabToFilter(activeStoryListTab))}
        />
      );
    }

    return (
      <HomeScreenContent
        featuredStory={featuredStory}
        stories={stories}
        storyListTabs={storyListTabs}
        activeStoryListTab={activeStoryListTab}
        onTabPress={setActiveStoryListTab}
        onStoryPress={handleStoryPress}
      />
    );
  };

  return (
    <SafeAreaView style={styles.container} edges={['top', 'left', 'right']}>
      <HeaderBar
        title={t('appName')}
        subtitle={t('homeScreen.subtitle')}
        rightElement={
          <TouchableOpacity
            style={styles.rankingButton}
            onPress={() => router.push('/rankings')}
          >
            <BarChart2 size={24} color={theme.colors.text} />
          </TouchableOpacity>
        }
      />

      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        <View style={styles.searchBarContainer}>
          <SearchBar
            value={searchQuery}
            onChangeText={setSearchQuery}
            onSearch={handleSearch}
            placeholder={t(
              'homeScreen.searchPlaceholder',
              '搜索故事、作者、标签...'
            )}
          />
        </View>

        <Text style={styles.sectionTitle}>
          {t('homeScreen.sectionTitle.themes')}
        </Text>
        <ThemeCarousel themes={themes} onThemePress={handleThemePress} />

        {renderContent()}
      </ScrollView>
    </SafeAreaView>
  );
}
