import { StyleSheet } from 'react-native';
import { AppTheme } from '@/lib/theme/themes';

export const createStyles = (theme: AppTheme) => StyleSheet.create({
  container: {
    padding: theme.spacing.md,
    borderRadius: theme.roundness,
    borderWidth: StyleSheet.hairlineWidth,
    borderColor: theme.colors.border,
    backgroundColor: theme.colors.card,
    flexDirection: 'row',
    alignItems: 'center',
    // Add shadow if needed
  },
  icon: {
    marginRight: theme.spacing.sm,
  },
  suggestionText: {
    fontFamily: theme.fonts.medium,
    fontSize: 16,
    color: theme.colors.text,
    flex: 1, // Allow text to take available space
  },
});
