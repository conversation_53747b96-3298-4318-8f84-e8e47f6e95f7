import React from 'react';
import { View } from 'react-native';
import { useAppTheme } from '@/hooks/useAppTheme';
import { createStyles } from './StoryGrid.styles';
import StoryCard, { Story } from '@/components/stories/StoryCard'; // Use existing card

interface StoryGridProps {
  stories: Story[];
  onStoryPress?: (storyId: string) => void;
}

export function StoryGrid({ stories, onStoryPress }: StoryGridProps) {
  const theme = useAppTheme();
  const styles = createStyles(theme);

  return (
    <View style={styles.grid}>
      {stories.map(story => (
        <StoryCard 
          key={story.id} 
          story={story} 
          onPress={() => onStoryPress?.(story.id)}
        />
      ))}
      {/* Add Empty state or Loading state if needed */}
    </View>
  );
} 