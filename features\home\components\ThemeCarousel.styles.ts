import { StyleSheet } from 'react-native';
import { AppTheme } from '@/lib/theme/themes';

export const createStyles = (theme: AppTheme) => StyleSheet.create({
  container: {
    // No container-specific styles needed, margin/padding handled by parent
  },
  scrollViewContent: {
    paddingVertical: theme.spacing.xs, // Small vertical padding for cards
    paddingLeft: theme.spacing.md, // Align first card with screen padding
    paddingRight: theme.spacing.sm, // Allow last card's shadow to show
    gap: theme.spacing.sm,
  },
}); 