import { StyleSheet } from 'react-native';
import { useAppTheme } from '@/hooks/useAppTheme';

export const createStyles = (theme: ReturnType<typeof useAppTheme>) =>
  StyleSheet.create({
    container: {
      borderRadius: theme.radius.lg,
      overflow: 'hidden',
      marginBottom: theme.spacing.md,
    },
    coverImage: {
      height: 250,
      justifyContent: 'flex-end',
    },
    coverImageStyle: {
      borderRadius: theme.radius.lg,
    },
    overlay: {
      backgroundColor: 'rgba(0, 0, 0, 0.6)',
      paddingHorizontal: theme.spacing.md,
      paddingVertical: theme.spacing.lg,
    },
    premiumBadge: {
      position: 'absolute',
      top: theme.spacing.md,
      right: theme.spacing.md,
      backgroundColor: '#FFD700',
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: theme.spacing.xs,
      paddingVertical: 2,
      borderRadius: theme.radius.round,
    },
    premiumText: {
      fontFamily: theme.fonts.bold,
      color: '#000',
      fontSize: 10,
      marginLeft: 2,
    },
    contentContainer: {
      gap: theme.spacing.xs,
    },
    title: {
      fontFamily: theme.fonts.storyTitle,
      fontSize: theme.fontSizes.xl,
      color: '#FFFFFF',
      marginBottom: theme.spacing.xs,
    },
    infoRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: theme.spacing.xs,
    },
    authorName: {
      fontFamily: theme.fonts.medium,
      fontSize: theme.fontSizes.sm,
      color: '#FFFFFF',
      opacity: 0.9,
    },
    statsContainer: {
      flexDirection: 'row',
      gap: theme.spacing.sm,
    },
    stat: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 4,
    },
    statText: {
      color: '#FFFFFF',
      fontSize: theme.fontSizes.xs,
      fontFamily: theme.fonts.regular,
    },
    summary: {
      fontFamily: theme.fonts.regular,
      fontSize: theme.fontSizes.sm,
      color: '#FFFFFF',
      opacity: 0.9,
      marginBottom: theme.spacing.sm,
    },
    themesContainer: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: theme.spacing.xs,
      marginBottom: theme.spacing.md,
    },
    themeTag: {
      backgroundColor: 'rgba(255, 255, 255, 0.25)',
      paddingHorizontal: theme.spacing.xs,
      paddingVertical: 2,
      borderRadius: theme.radius.sm,
    },
    themeText: {
      fontFamily: theme.fonts.regular,
      fontSize: theme.fontSizes.xs,
      color: '#FFFFFF',
    },
    readButton: {
      paddingVertical: theme.spacing.sm,
      borderRadius: theme.radius.md,
      alignItems: 'center',
    },
    readButtonText: {
      fontFamily: theme.fonts.bold,
      fontSize: theme.fontSizes.sm,
    },
  });
