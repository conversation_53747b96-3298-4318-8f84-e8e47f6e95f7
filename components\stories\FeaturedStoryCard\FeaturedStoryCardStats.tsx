import React from 'react';
import { View, Text } from 'react-native';
import { Eye, Heart } from 'lucide-react-native';
import { createStyles } from './styles';
import { useAppTheme } from '@/hooks/useAppTheme';

interface FeaturedStoryCardStatsProps {
  authorName: string;
  views: number;
  likes: number;
}

export function FeaturedStoryCardStats({
  authorName,
  views,
  likes,
}: FeaturedStoryCardStatsProps) {
  const appTheme = useAppTheme();
  const styles = createStyles(appTheme);

  return (
    <View style={styles.infoRow}>
      <Text style={styles.authorName}>@{authorName}</Text>

      <View style={styles.statsContainer}>
        <View style={styles.stat}>
          <Eye size={12} color="#FFFFFF" />
          <Text style={styles.statText}>{views}</Text>
        </View>

        <View style={styles.stat}>
          <Heart size={12} color="#FFFFFF" fill="#FFFFFF" />
          <Text style={styles.statText}>{likes}</Text>
        </View>
      </View>
    </View>
  );
}
