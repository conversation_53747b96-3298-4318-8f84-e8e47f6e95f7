import React, { useMemo } from 'react';
import { View, ScrollView, KeyboardAvoidingView, Platform } from 'react-native';
import { useAppTheme } from '@/hooks/useAppTheme';
import { createStyles } from './CreateStoryScreen.styles';

// Custom hooks
import { useCreateStory } from '../hooks/useCreateStory';
import { useAISuggestions } from '../hooks/useAISuggestions';

// Components
import CreateStoryHeader from '../components/CreateStoryHeader';
import CreateStoryForm from '../components/CreateStoryForm';
import AISuggestionsSection from '../components/AISuggestionsSection';

export default function CreateStoryScreen() {
  const theme = useAppTheme();
  const styles = useMemo(() => createStyles(theme), [theme]);

  // Story creation state and logic
  const {
    title,
    setTitle,
    initialContent,
    setInitialContent,
    submitting,
    contentFocused,
    setContentFocused,
    initialContentIsAI,
    setInitialContentIsAI,
    isFormValid,
    handleSubmit,
  } = useCreateStory();

  // AI suggestions state and logic
  const {
    aiSuggestions,
    loadingAISuggestions,
    showAISuggestions,
    handleFetchAISuggestions,
    handleSelectAISuggestion,
  } = useAISuggestions({
    getPrompt: () => `标题：${title}\n内容：${initialContent}`,
    onSelectSuggestion: (suggestion) => {
      setInitialContent((prevContent) => {
        const newContent = prevContent
          ? `${prevContent}\n${suggestion}`
          : suggestion;
        if (suggestion.trim().length > 0) {
          setInitialContentIsAI(true);
        }
        return newContent;
      });
    },
  });

  return (
    <KeyboardAvoidingView
      style={{ flex: 1 }}
      behavior={Platform.OS === 'ios' ? 'padding' : undefined}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 64 : 0}
    >
      <ScrollView
        style={styles.container}
        keyboardShouldPersistTaps="handled"
        contentContainerStyle={styles.scrollContent}
      >
        <CreateStoryHeader />

        <CreateStoryForm
          title={title}
          onTitleChange={setTitle}
          initialContent={initialContent}
          onContentChange={setInitialContent}
          contentFocused={contentFocused}
          onContentFocus={() => setContentFocused(true)}
          onContentBlur={() => setContentFocused(false)}
          isFormValid={isFormValid}
          submitting={submitting}
          onSubmit={handleSubmit}
        />

        <AISuggestionsSection
          onFetchSuggestions={handleFetchAISuggestions}
          loadingSuggestions={loadingAISuggestions}
          showSuggestions={showAISuggestions}
          suggestions={aiSuggestions}
          onSelectSuggestion={handleSelectAISuggestion}
          disabled={submitting}
        />
      </ScrollView>
    </KeyboardAvoidingView>
  );
}
