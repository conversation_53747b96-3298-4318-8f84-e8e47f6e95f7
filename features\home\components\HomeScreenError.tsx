import React from 'react';
import { View, Text, Button, StyleSheet } from 'react-native';
import { useTranslation } from 'react-i18next';
import { useAppTheme } from '@/hooks/useAppTheme';

interface HomeScreenErrorProps {
  error: string;
  onRetry: () => void;
}

export function HomeScreenError({ error, onRetry }: HomeScreenErrorProps) {
  const theme = useAppTheme();
  const { t } = useTranslation();
  
  return (
    <View style={styles.container}>
      <Text style={[styles.errorText, { color: theme.colors.error }]}>
        {error}
      </Text>
      <Button
        title={t('tryAgain', '重试')}
        onPress={onRetry}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 20,
  },
  errorText: {
    marginBottom: 16,
    textAlign: 'center',
  },
});
