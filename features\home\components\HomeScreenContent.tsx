import React from 'react';
import { View, Text } from 'react-native';
import { useTranslation } from 'react-i18next';
import { useAppTheme } from '@/hooks/useAppTheme';
import { Story as ApiStory } from '@/api/stories';
import { FeaturedStory } from './FeaturedStory';
import { StoryListTabs } from './StoryListTabs';
import { StoryGrid } from './StoryGrid';
import { createStyles } from '../screens/HomeScreen.styles';

interface HomeScreenContentProps {
  featuredStory: ApiStory | null;
  stories: ApiStory[];
  storyListTabs: string[];
  activeStoryListTab: string;
  onTabPress: (tab: string) => void;
  onStoryPress: (storyId: string) => void;
}

export function HomeScreenContent({
  featuredStory,
  stories,
  storyListTabs,
  activeStoryListTab,
  onTabPress,
  onStoryPress,
}: HomeScreenContentProps) {
  const theme = useAppTheme();
  const styles = createStyles(theme);
  const { t } = useTranslation();

  return (
    <>
      {featuredStory && (
        <>
          <Text style={styles.sectionTitle}>
            {t('homeScreen.sectionTitle.featured')}
          </Text>
          <FeaturedStory
            story={featuredStory}
            onPress={() => onStoryPress(featuredStory.id)}
          />
        </>
      )}

      <StoryListTabs
        tabs={storyListTabs}
        activeTab={activeStoryListTab}
        onTabPress={onTabPress}
      />
      <StoryGrid stories={stories} onStoryPress={onStoryPress} />
    </>
  );
}
