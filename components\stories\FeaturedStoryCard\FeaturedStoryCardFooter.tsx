import React from 'react';
import { TouchableOpacity, Text } from 'react-native';
import { createStyles } from './styles';
import { useAppTheme } from '@/hooks/useAppTheme';

interface FeaturedStoryCardFooterProps {
  onPress?: () => void;
}

export function FeaturedStoryCardFooter({
  onPress,
}: FeaturedStoryCardFooterProps) {
  const appTheme = useAppTheme();
  const styles = createStyles(appTheme);

  return (
    <TouchableOpacity
      style={[styles.readButton, { backgroundColor: appTheme.colors.primary }]}
      onPress={onPress}
    >
      <Text
        style={[styles.readButtonText, { color: appTheme.colors.onPrimary }]}
      >
        开始阅读
      </Text>
    </TouchableOpacity>
  );
}
