import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { useAppTheme } from '@/hooks/useAppTheme';
import { createStyles } from './StoryListTabs.styles';

interface StoryListTabsProps {
  tabs: string[];
  activeTab: string;
  onTabPress: (tab: string) => void;
}

export function StoryListTabs({ tabs, activeTab, onTabPress }: StoryListTabsProps) {
  const theme = useAppTheme();
  const styles = createStyles(theme);

  return (
    <View style={styles.tabsContainer}>
      {tabs.map(tab => {
        const isActive = activeTab === tab;
        const textColor = isActive ? theme.colors.primary : theme.colors.secondaryText;
        const borderBottomColor = isActive ? theme.colors.primary : 'transparent';

        return (
          <TouchableOpacity 
            key={tab}
            style={[styles.tab, { borderBottomColor }]}
            onPress={() => onTabPress(tab)}
          >
            <Text style={[styles.tabText, { color: textColor }]}>
              {tab}
            </Text>
          </TouchableOpacity>
        );
      })}
    </View>
  );
} 