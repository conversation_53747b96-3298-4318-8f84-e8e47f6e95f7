import { StyleSheet, Dimensions } from 'react-native';
import { AppTheme } from '@/lib/theme/themes';

export const createStyles = (theme: AppTheme) => {
  const { width } = Dimensions.get('window');
  const cardWidth = (width - theme.spacing.md * 3) / 2;

  return StyleSheet.create({
    container: {
      width: cardWidth,
      borderRadius: theme.radius.md,
      marginBottom: theme.spacing.md,
      borderWidth: 1,
      overflow: 'hidden',
    },
    imageContainer: {
      position: 'relative',
    },
    coverImage: {
      width: '100%',
      height: cardWidth * 0.75,
      borderTopLeftRadius: theme.radius.md,
      borderTopRightRadius: theme.radius.md,
    },
    premiumBadge: {
      position: 'absolute',
      top: theme.spacing.xs,
      right: theme.spacing.xs,
      backgroundColor: '#FFD700',
      padding: 4,
      borderRadius: theme.radius.round,
    },
    content: {
      padding: theme.spacing.sm,
    },
    title: {
      fontFamily: theme.fonts.bold,
      fontSize: theme.fontSizes.md,
      marginBottom: 2,
    },
    author: {
      fontFamily: theme.fonts.regular,
      fontSize: theme.fontSizes.xs,
      marginBottom: theme.spacing.xs,
    },
    statsRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginTop: theme.spacing.xs,
    },
    stat: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 4,
    },
    statText: {
      fontSize: theme.fontSizes.xs,
      fontFamily: theme.fonts.regular,
    },
  });
};
