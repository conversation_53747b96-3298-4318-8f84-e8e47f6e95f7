import React from 'react';
import { ActivityIndicator, View, StyleSheet } from 'react-native';
import { useAppTheme } from '@/hooks/useAppTheme';

export function HomeScreenLoading() {
  const theme = useAppTheme();
  
  return (
    <View style={styles.container}>
      <ActivityIndicator
        size="large"
        color={theme.colors.primary}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 20,
  },
});
