import React from 'react';
import { View, Text, TouchableOpacity, ActivityIndicator } from 'react-native';
import { useTranslation } from 'react-i18next';
import { createStyles } from './styles';
import { useAppTheme } from '@/hooks/useAppTheme';
import AiSuggestionCard from '@/components/creation/AiSuggestionCard';

interface AISuggestionsSectionProps {
  onFetchSuggestions: () => void;
  loadingSuggestions: boolean;
  showSuggestions: boolean;
  suggestions: string[];
  onSelectSuggestion: (suggestion: string) => void;
  disabled?: boolean;
}

export default function AISuggestionsSection({
  onFetchSuggestions,
  loadingSuggestions,
  showSuggestions,
  suggestions,
  onSelectSuggestion,
  disabled = false,
}: AISuggestionsSectionProps) {
  const { t } = useTranslation();
  const theme = useAppTheme();
  const styles = createStyles(theme);

  return (
    <View style={styles.formSection}>
      <TouchableOpacity
        style={[styles.button, styles.aiButton]}
        onPress={onFetchSuggestions}
        disabled={loadingSuggestions || disabled}
      >
        {loadingSuggestions ? (
          <ActivityIndicator color={theme.colors.onPrimary} />
        ) : (
          <Text style={styles.buttonText}>
            {t('storyForm.getAISuggestions', '获取 AI 建议')}
          </Text>
        )}
      </TouchableOpacity>

      {showSuggestions && loadingSuggestions && (
        <ActivityIndicator
          size="large"
          color={theme.colors.primary}
          style={{ marginTop: theme.spacing.md }}
        />
      )}

      {showSuggestions &&
        !loadingSuggestions &&
        suggestions.length > 0 && (
          <View style={{ marginTop: theme.spacing.md }}>
            <Text style={styles.label}>
              {t('aiSuggestions.title', 'AI 建议:')}
            </Text>
            {suggestions.map((suggestion, index) => (
              <AiSuggestionCard
                key={index}
                suggestion={suggestion}
                onSelect={onSelectSuggestion}
              />
            ))}
          </View>
        )}
      {showSuggestions &&
        !loadingSuggestions &&
        suggestions.length === 0 && (
          <Text
            style={[
              styles.tipText,
              { marginTop: theme.spacing.md, textAlign: 'center' },
            ]}
          >
            {t(
              'aiSuggestions.noSuggestions',
              '暂时没有合适的建议，尝试修改你的输入或稍后再试。'
            )}
          </Text>
        )}
    </View>
  );
}
