import React from 'react';
import { View, Text } from 'react-native';
import { createStyles } from './styles';
import { useAppTheme } from '@/hooks/useAppTheme';

interface FeaturedStoryCardContentProps {
  summary: string;
  themeTags: string[];
}

export function FeaturedStoryCardContent({
  summary,
  themeTags,
}: FeaturedStoryCardContentProps) {
  const appTheme = useAppTheme();
  const styles = createStyles(appTheme);

  return (
    <>
      <Text style={styles.summary} numberOfLines={2}>
        {summary}
      </Text>

      <View style={styles.themesContainer}>
        {themeTags.map((tag) => (
          <View key={tag} style={styles.themeTag}>
            <Text style={styles.themeText}>{tag}</Text>
          </View>
        ))}
      </View>
    </>
  );
}
