import React from 'react';
import { View, Text } from 'react-native';
import { useTranslation } from 'react-i18next';
import { createStyles } from './styles';
import { useAppTheme } from '@/hooks/useAppTheme';

export default function CreateStoryHeader() {
  const { t } = useTranslation();
  const theme = useAppTheme();
  const styles = createStyles(theme);

  return (
    <View style={styles.header}>
      <Text style={styles.title}>
        {t('createStoryTitle', '创建新故事')}
      </Text>
      <Text style={styles.subtitle}>
        {t('storyForm.createDescription', '开始你的创作之旅，写下你的故事')}
      </Text>
    </View>
  );
}
