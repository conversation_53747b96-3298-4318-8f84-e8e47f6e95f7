import React from 'react';
import { View, Text, TextInput, TouchableOpacity, ActivityIndicator } from 'react-native';
import { useTranslation } from 'react-i18next';
import { Feather } from '@expo/vector-icons';
import { createStyles } from './styles';
import { useAppTheme } from '@/hooks/useAppTheme';

interface CreateStoryFormProps {
  title: string;
  onTitleChange: (text: string) => void;
  initialContent: string;
  onContentChange: (text: string) => void;
  contentFocused: boolean;
  onContentFocus: () => void;
  onContentBlur: () => void;
  isFormValid: boolean;
  submitting: boolean;
  onSubmit: () => void;
}

export default function CreateStoryForm({
  title,
  onTitleChange,
  initialContent,
  onContentChange,
  contentFocused,
  onContentFocus,
  onContentBlur,
  isFormValid,
  submitting,
  onSubmit,
}: CreateStoryFormProps) {
  const { t } = useTranslation();
  const theme = useAppTheme();
  const styles = createStyles(theme);

  return (
    <>
      <View style={styles.formSection}>
        <View style={styles.labelContainer}>
          <Feather name="bookmark" size={18} color={theme.colors.primary} />
          <Text style={styles.label}>
            {t('storyForm.titleLabel', '标题')}
          </Text>
        </View>
        <TextInput
          style={[styles.input, title.length > 0 && styles.inputFilled]}
          value={title}
          onChangeText={onTitleChange}
          placeholder={t('storyForm.titlePlaceholder', '输入故事标题')}
          placeholderTextColor={theme.colors.placeholderText}
          maxLength={100}
        />
        {title.length > 0 && (
          <Text style={styles.characterCount}>{title.length}/100</Text>
        )}
      </View>

      <View style={styles.formSection}>
        <View style={styles.labelContainer}>
          <Feather name="book-open" size={18} color={theme.colors.primary} />
          <Text style={styles.label}>
            {t('storyForm.initialContentLabel', '开始你的故事')}
          </Text>
        </View>
        <TextInput
          style={[
            styles.input,
            styles.contentInput,
            contentFocused && styles.inputFocused,
            initialContent.length > 0 && styles.inputFilled,
          ]}
          value={initialContent}
          onChangeText={onContentChange}
          placeholder={t('storyForm.initialContentPlaceholder', '从前...')}
          placeholderTextColor={theme.colors.placeholderText}
          multiline
          onFocus={onContentFocus}
          onBlur={onContentBlur}
        />
        {initialContent.length > 0 && (
          <View style={styles.contentFeedback}>
            <Text
              style={[
                styles.characterCount,
                initialContent.length < 50
                  ? styles.characterCountWarning
                  : styles.characterCountSuccess,
              ]}
            >
              {initialContent.length < 50
                ? `${t('storyForm.contentMinLength', '至少需要50个字符')} (${
                    initialContent.length
                  }/50)`
                : `${initialContent.length} ${t(
                    'storyForm.characters',
                    '个字符'
                  )}`}
            </Text>
          </View>
        )}
      </View>

      <View style={styles.tipContainer}>
        <Feather name="info" size={16} color={theme.colors.secondary} />
        <Text style={styles.tipText}>
          {t(
            'storyForm.tip',
            '提示：一个好的开头能够吸引读者继续阅读你的故事。'
          )}
        </Text>
      </View>

      <TouchableOpacity
        style={[styles.button, !isFormValid && styles.buttonDisabled]}
        onPress={onSubmit}
        disabled={submitting || !isFormValid}
      >
        {submitting ? (
          <ActivityIndicator
            size="small"
            color={theme.dark ? theme.colors.surface : '#FFFFFF'}
          />
        ) : (
          <Text style={styles.buttonText}>
            {t('storyForm.submitButton', '创建故事')}
          </Text>
        )}
      </TouchableOpacity>
    </>
  );
}
