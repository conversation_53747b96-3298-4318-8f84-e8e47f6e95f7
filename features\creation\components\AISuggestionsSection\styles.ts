import { StyleSheet } from 'react-native';
import { AppTheme } from '@/lib/theme/themes';

export const createStyles = (theme: AppTheme) => StyleSheet.create({
  formSection: {
    marginBottom: theme.spacing.lg,
  },
  button: {
    backgroundColor: theme.colors.primary,
    borderRadius: theme.roundness,
    padding: theme.spacing.md,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: theme.spacing.lg,
  },
  aiButton: {
    backgroundColor: theme.colors.accent,
  },
  buttonText: {
    color: theme.colors.onPrimary,
    fontSize: 16,
    fontFamily: theme.fonts.medium,
  },
  label: {
    fontSize: 16,
    fontFamily: theme.fonts.medium,
    color: theme.colors.text,
    marginBottom: theme.spacing.sm,
  },
  tipText: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginLeft: theme.spacing.sm,
    flex: 1,
  },
});
