import React from 'react';
import {
  View,
  Text,
  Image,
  TouchableOpacity,
} from 'react-native';
import { useRouter } from 'expo-router';
import { useAppTheme } from '@/hooks/useAppTheme';
import { Story as ApiStory } from '@/api/stories';
import { PremiumBadge } from './PremiumBadge';
import { StoryStats } from './StoryStats';
import { createStyles } from './styles';

export type { ApiStory as Story };

interface StoryCardProps {
  story: ApiStory;
  onPress?: (storyId: string) => void;
  style?: any; // Allow passing additional styles
}

export default function StoryCard({ story, onPress, style }: StoryCardProps) {
  const theme = useAppTheme();
  const styles = createStyles(theme);
  const router = useRouter();

  const coverImageUrl = story.cover_image_url;
  const authorName = story.profiles?.username || 'Unknown';
  const views = 0;
  const likes = story.like_count || 0;
  const isPremium = false;

  const handlePress = () => {
    if (onPress) {
      onPress(story.id);
    } else {
      router.push(`/stories/${story.id}`);
    }
  };

  return (
    <TouchableOpacity
      onPress={handlePress}
      style={[
        styles.container,
        {
          backgroundColor: theme.colors.card,
          borderColor: theme.colors.border,
        },
        theme.shadows.sm,
        style,
      ]}
    >
      <View style={styles.imageContainer}>
        <Image
          source={
            coverImageUrl
              ? { uri: coverImageUrl }
              : require('../../../assets/images/default-story-placeholder.png')
          }
          style={styles.coverImage}
        />

        <PremiumBadge visible={isPremium} />
      </View>

      <View style={styles.content}>
        <Text
          style={[styles.title, { color: theme.colors.text }]}
          numberOfLines={1}
        >
          {story.title}
        </Text>

        <Text
          style={[styles.author, { color: theme.colors.secondaryText }]}
          numberOfLines={1}
        >
          @{authorName}
        </Text>

        <StoryStats views={views} likes={likes} />
      </View>
    </TouchableOpacity>
  );
}
