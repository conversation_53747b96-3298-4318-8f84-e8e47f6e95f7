import React from 'react';
import { View } from 'react-native';
import { useAppTheme } from '@/hooks/useAppTheme';
import { createStyles } from './FeaturedStory.styles';
// import FeaturedStoryCard, { Story } from '@/components/stories/FeaturedStoryCard'; // Use existing card
import FeaturedStoryCard from '@/components/stories/FeaturedStoryCard';
import { Story } from '@/api/stories'; // Import Story type directly from API

interface FeaturedStoryProps {
  story: Story; // Assuming Story type is exported from FeaturedStoryCard or a shared type file
  onPress?: (storyId: string) => void;
}

export function FeaturedStory({ story, onPress }: FeaturedStoryProps) {
  const theme = useAppTheme();
  const styles = createStyles(theme);

  // Handle case where story might be null or undefined
  if (!story) {
    return null; 
  }

  return (
    <View style={styles.container}>
      <FeaturedStoryCard 
        story={story} 
        onPress={() => onPress?.(story.id)} 
      />
    </View>
  );
} 