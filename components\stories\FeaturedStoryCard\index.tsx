import React from 'react';
import { View, ImageBackground, TouchableOpacity } from 'react-native';
import { useAppTheme } from '@/hooks/useAppTheme';
import { Story as ApiStory } from '@/api/stories';
import { createStyles } from './styles';
import { FeaturedStoryCardHeader } from './FeaturedStoryCardHeader';
import { FeaturedStoryCardStats } from './FeaturedStoryCardStats';
import { FeaturedStoryCardContent } from './FeaturedStoryCardContent';
import { FeaturedStoryCardFooter } from './FeaturedStoryCardFooter';

interface FeaturedStoryCardProps {
  story: ApiStory;
  onPress?: () => void;
}

export default function FeaturedStoryCard({
  story,
  onPress,
}: FeaturedStoryCardProps) {
  const appTheme = useAppTheme();
  const styles = createStyles(appTheme);

  const coverImageUrl = story.cover_image_url;
  const isPremium = false;
  const authorName = story.profiles?.username || 'Unknown';
  const views = 0;
  const likes = story.like_count || 0;
  const summary = story.first_segment_content || '';
  const themeTags = story.tags || [];

  return (
    <TouchableOpacity
      style={[styles.container, appTheme.shadows.md]}
      onPress={onPress}
    >
      <ImageBackground
        source={
          coverImageUrl
            ? { uri: coverImageUrl }
            : require('../../../assets/images/default-story-placeholder.png')
        }
        style={styles.coverImage}
        imageStyle={styles.coverImageStyle}
      >
        <View style={styles.overlay}>
          <View style={styles.contentContainer}>
            <FeaturedStoryCardHeader
              title={story.title}
              isPremium={isPremium}
            />

            <FeaturedStoryCardStats
              authorName={authorName}
              views={views}
              likes={likes}
            />

            <FeaturedStoryCardContent summary={summary} themeTags={themeTags} />

            <FeaturedStoryCardFooter onPress={onPress} />
          </View>
        </View>
      </ImageBackground>
    </TouchableOpacity>
  );
}
