import { StyleSheet } from 'react-native';
import { AppTheme } from '@/lib/theme/themes';

// Styles for the main HomeScreen layout
export const createStyles = (theme: AppTheme) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    scrollView: {
      flex: 1,
    },
    scrollContent: {
      paddingHorizontal: theme.spacing.md,
      paddingBottom: theme.spacing.xxl, // Ensure enough space at the bottom
    },
    searchBarContainer: {
      // Add container for SearchBar margins
      marginBottom: theme.spacing.md, // Space below search bar
      marginTop: theme.spacing.sm, // Space above search bar
    },
    sectionTitle: {
      // Shared style for section titles
      fontFamily: theme.fonts.bold,
      fontSize: 20, // Slightly larger title
      color: theme.colors.text,
      marginTop: theme.spacing.lg,
      marginBottom: theme.spacing.md,
    },
    // 添加错误状态相关样式
    centeredMessageContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: theme.spacing.lg,
      marginTop: theme.spacing.xl,
    },
    errorText: {
      color: theme.colors.error,
      fontSize: 16,
      textAlign: 'center',
      marginBottom: theme.spacing.md,
      fontFamily: theme.fonts.medium,
    },
    // Styles for carousel, featured, tabs, grid will be in component files
    rankingButton: {
      padding: theme.spacing.xs,
    },
  });
