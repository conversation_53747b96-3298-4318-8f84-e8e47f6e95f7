import React from 'react';
import { TouchableOpacity, Text } from 'react-native';
import { useAppTheme } from '@/hooks/useAppTheme';
import { StoryTheme } from '@/types/story';
import {
  BookOpen,
  Rocket,
  Wand,
  Landmark,
  Heart,
  Search,
  Building,
} from 'lucide-react-native';
import { createStyles } from './styles';

// Interface for the theme data passed as prop
export interface Theme {
  id: string;
  name: string;
  icon?: string; // Added icon based on switch usage
  color?: string; // Optional specific background color
}

interface ThemeCardProps {
  theme: Theme; // Use the defined interface
  onPress?: (themeId: string) => void;
}

export default function ThemeCard({
  theme: themeProp,
  onPress,
}: ThemeCardProps) {
  // Renamed prop to avoid conflict
  const appTheme = useAppTheme(); // Get the application theme
  const styles = createStyles(appTheme); // Create styles using application theme

  const renderIcon = () => {
    // TODO: Icon color should ideally adapt to themeProp.color background for contrast
    const iconColor = '#FFFFFF'; // Hardcoded white - potential readability issue
    const size = 18;

    // Use themeProp for icon logic
    switch (themeProp.icon) {
      case 'rocket':
        return <Rocket size={size} color={iconColor} />;
      case 'wand':
        return <Wand size={size} color={iconColor} />;
      case 'search':
        return <Search size={size} color={iconColor} />;
      case 'heart':
        return <Heart size={size} color={iconColor} />;
      case 'landmark':
        return <Landmark size={size} color={iconColor} />;
      case 'building':
        return <Building size={size} color={iconColor} />;
      default:
        return <BookOpen size={size} color={iconColor} />;
    }
  };

  return (
    <TouchableOpacity
      // Use themeProp.color for background, fallback to a theme color if needed?
      style={[
        styles.container,
        { backgroundColor: themeProp.color || appTheme.colors.primary },
      ]} // Added fallback color
      onPress={() => onPress?.(themeProp.id)}
    >
      {renderIcon()}
      {/* TODO: Text color should also adapt to background for contrast */}
      <Text style={styles.themeName}>{themeProp.name}</Text>
    </TouchableOpacity>
  );
}
