import React, { createContext, useMemo } from 'react';
import { PaperProvider } from 'react-native-paper';
// Update store and theme import paths
import { useSettingsStore } from '@/lib/store/settingsStore';
import { lightTheme, darkTheme, AppTheme } from './themes';
import { paperLightTheme, paperDarkTheme } from './paperThemes';

interface ThemeContextProps {
  theme: AppTheme;
}

export const ThemeContext = createContext<ThemeContextProps>({
  theme: lightTheme, // Default theme
});

export const ThemeProvider: React.FC<React.PropsWithChildren<{}>> = ({
  children,
}) => {
  const themeMode = useSettingsStore((state) => state.themeMode);
  const systemColorScheme = useSettingsStore(
    (state) => state.systemColorScheme
  );
  const hasHydrated = useSettingsStore((state) => state._hasHydrated);

  // 计算当前主题模式
  const currentMode = useMemo(() => {
    if (!hasHydrated) {
      return 'light'; // 默认亮色模式
    }
    return themeMode === 'system' ? systemColorScheme : themeMode;
  }, [themeMode, systemColorScheme, hasHydrated]);

  // 自定义主题（用于现有组件）
  const activeTheme = useMemo(() => {
    return currentMode === 'dark' ? darkTheme : lightTheme;
  }, [currentMode]);

  // react-native-paper 主题
  const paperTheme = useMemo(() => {
    return currentMode === 'dark' ? paperDarkTheme : paperLightTheme;
  }, [currentMode]);

  return (
    <ThemeContext.Provider value={{ theme: activeTheme }}>
      <PaperProvider theme={paperTheme}>{children}</PaperProvider>
    </ThemeContext.Provider>
  );
};
