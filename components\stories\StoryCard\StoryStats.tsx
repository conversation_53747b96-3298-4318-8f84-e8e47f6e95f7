import React from 'react';
import { View, Text } from 'react-native';
import { Eye, Heart } from 'lucide-react-native';
import { useAppTheme } from '@/hooks/useAppTheme';
import { createStyles } from './styles';

interface StoryStatsProps {
  views: number;
  likes: number;
}

export function StoryStats({ views, likes }: StoryStatsProps) {
  const theme = useAppTheme();
  const styles = createStyles(theme);

  return (
    <View style={styles.statsRow}>
      <View style={styles.stat}>
        <Eye size={12} color={theme.colors.secondaryText} />
        <Text
          style={[
            styles.statText,
            { color: theme.colors.secondaryText },
          ]}
        >
          {views}
        </Text>
      </View>

      <View style={styles.stat}>
        <Heart size={12} color={theme.colors.secondaryText} />
        <Text
          style={[
            styles.statText,
            { color: theme.colors.secondaryText },
          ]}
        >
          {likes}
        </Text>
      </View>
    </View>
  );
}
