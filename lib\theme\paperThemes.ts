import { MD3LightTheme, MD3DarkTheme } from 'react-native-paper';
import type { MD3Theme } from 'react-native-paper';
import { lightTheme, darkTheme } from './themes';

/**
 * 将自定义主题转换为 react-native-paper 兼容的 MD3 主题
 * 保持与现有主题系统的兼容性
 */

// 创建基于现有 lightTheme 的 Paper 主题
export const paperLightTheme: MD3Theme = {
  ...MD3LightTheme,
  colors: {
    ...MD3LightTheme.colors,
    // 映射现有主题颜色到 MD3 颜色系统
    primary: lightTheme.colors.primary,
    onPrimary: lightTheme.colors.onPrimary,
    primaryContainer: lightTheme.colors.accent,
    onPrimaryContainer: lightTheme.colors.text,
    secondary: lightTheme.colors.accent,
    onSecondary: lightTheme.colors.onPrimary,
    secondaryContainer: lightTheme.colors.accent1,
    onSecondaryContainer: lightTheme.colors.text,
    tertiary: lightTheme.colors.accent2,
    onTertiary: lightTheme.colors.onPrimary,
    tertiaryContainer: lightTheme.colors.accent2,
    onTertiaryContainer: lightTheme.colors.text,
    surface: lightTheme.colors.surface,
    onSurface: lightTheme.colors.onSurface,
    surfaceVariant: lightTheme.colors.card,
    onSurfaceVariant: lightTheme.colors.text,
    background: lightTheme.colors.background,
    onBackground: lightTheme.colors.text,
    error: lightTheme.colors.error,
    onError: lightTheme.colors.onPrimary,
    errorContainer: lightTheme.colors.error,
    onErrorContainer: lightTheme.colors.onPrimary,
    outline: lightTheme.colors.border,
    outlineVariant: lightTheme.colors.border,
    shadow: lightTheme.colors.backdrop,
    scrim: lightTheme.colors.backdrop,
    inverseSurface: lightTheme.colors.text,
    inverseOnSurface: lightTheme.colors.background,
    inversePrimary: lightTheme.colors.accent,
    backdrop: lightTheme.colors.backdrop,
    // 保留现有的自定义颜色
    notification: lightTheme.colors.notification,
    // MD3 elevation 系统
    elevation: {
      level0: 'transparent',
      level1: lightTheme.colors.surface,
      level2: lightTheme.colors.card,
      level3: lightTheme.colors.surface,
      level4: lightTheme.colors.surface,
      level5: lightTheme.colors.surface,
    },
  },
  // 使用现有的字体配置
  fonts: {
    ...MD3LightTheme.fonts,
    // 映射现有字体到 MD3 字体系统
    displayLarge: {
      ...MD3LightTheme.fonts.displayLarge,
      fontFamily: lightTheme.fonts.bold,
    },
    displayMedium: {
      ...MD3LightTheme.fonts.displayMedium,
      fontFamily: lightTheme.fonts.bold,
    },
    displaySmall: {
      ...MD3LightTheme.fonts.displaySmall,
      fontFamily: lightTheme.fonts.bold,
    },
    headlineLarge: {
      ...MD3LightTheme.fonts.headlineLarge,
      fontFamily: lightTheme.fonts.bold,
    },
    headlineMedium: {
      ...MD3LightTheme.fonts.headlineMedium,
      fontFamily: lightTheme.fonts.bold,
    },
    headlineSmall: {
      ...MD3LightTheme.fonts.headlineSmall,
      fontFamily: lightTheme.fonts.bold,
    },
    titleLarge: {
      ...MD3LightTheme.fonts.titleLarge,
      fontFamily: lightTheme.fonts.medium,
    },
    titleMedium: {
      ...MD3LightTheme.fonts.titleMedium,
      fontFamily: lightTheme.fonts.medium,
    },
    titleSmall: {
      ...MD3LightTheme.fonts.titleSmall,
      fontFamily: lightTheme.fonts.medium,
    },
    bodyLarge: {
      ...MD3LightTheme.fonts.bodyLarge,
      fontFamily: lightTheme.fonts.regular,
    },
    bodyMedium: {
      ...MD3LightTheme.fonts.bodyMedium,
      fontFamily: lightTheme.fonts.regular,
    },
    bodySmall: {
      ...MD3LightTheme.fonts.bodySmall,
      fontFamily: lightTheme.fonts.regular,
    },
    labelLarge: {
      ...MD3LightTheme.fonts.labelLarge,
      fontFamily: lightTheme.fonts.medium,
    },
    labelMedium: {
      ...MD3LightTheme.fonts.labelMedium,
      fontFamily: lightTheme.fonts.medium,
    },
    labelSmall: {
      ...MD3LightTheme.fonts.labelSmall,
      fontFamily: lightTheme.fonts.regular,
    },
  },
  // 使用现有的圆角配置
  roundness: lightTheme.radius.md,
};

// 创建基于现有 darkTheme 的 Paper 主题
export const paperDarkTheme: MD3Theme = {
  ...MD3DarkTheme,
  colors: {
    ...MD3DarkTheme.colors,
    // 映射现有主题颜色到 MD3 颜色系统
    primary: darkTheme.colors.primary,
    onPrimary: darkTheme.colors.onPrimary,
    primaryContainer: darkTheme.colors.accent,
    onPrimaryContainer: darkTheme.colors.text,
    secondary: darkTheme.colors.accent,
    onSecondary: darkTheme.colors.onPrimary,
    secondaryContainer: darkTheme.colors.accent1,
    onSecondaryContainer: darkTheme.colors.text,
    tertiary: darkTheme.colors.accent2,
    onTertiary: darkTheme.colors.onPrimary,
    tertiaryContainer: darkTheme.colors.accent2,
    onTertiaryContainer: darkTheme.colors.text,
    surface: darkTheme.colors.surface,
    onSurface: darkTheme.colors.onSurface,
    surfaceVariant: darkTheme.colors.card,
    onSurfaceVariant: darkTheme.colors.text,
    background: darkTheme.colors.background,
    onBackground: darkTheme.colors.text,
    error: darkTheme.colors.error,
    onError: darkTheme.colors.onPrimary,
    errorContainer: darkTheme.colors.error,
    onErrorContainer: darkTheme.colors.onPrimary,
    outline: darkTheme.colors.border,
    outlineVariant: darkTheme.colors.border,
    shadow: darkTheme.colors.backdrop,
    scrim: darkTheme.colors.backdrop,
    inverseSurface: darkTheme.colors.text,
    inverseOnSurface: darkTheme.colors.background,
    inversePrimary: darkTheme.colors.accent,
    backdrop: darkTheme.colors.backdrop,
    // 保留现有的自定义颜色
    notification: darkTheme.colors.notification,
    // MD3 elevation 系统
    elevation: {
      level0: 'transparent',
      level1: darkTheme.colors.surface,
      level2: darkTheme.colors.card,
      level3: darkTheme.colors.surface,
      level4: darkTheme.colors.surface,
      level5: darkTheme.colors.surface,
    },
  },
  // 使用现有的字体配置
  fonts: {
    ...MD3DarkTheme.fonts,
    // 映射现有字体到 MD3 字体系统
    displayLarge: {
      ...MD3DarkTheme.fonts.displayLarge,
      fontFamily: darkTheme.fonts.bold,
    },
    displayMedium: {
      ...MD3DarkTheme.fonts.displayMedium,
      fontFamily: darkTheme.fonts.bold,
    },
    displaySmall: {
      ...MD3DarkTheme.fonts.displaySmall,
      fontFamily: darkTheme.fonts.bold,
    },
    headlineLarge: {
      ...MD3DarkTheme.fonts.headlineLarge,
      fontFamily: darkTheme.fonts.bold,
    },
    headlineMedium: {
      ...MD3DarkTheme.fonts.headlineMedium,
      fontFamily: darkTheme.fonts.bold,
    },
    headlineSmall: {
      ...MD3DarkTheme.fonts.headlineSmall,
      fontFamily: darkTheme.fonts.bold,
    },
    titleLarge: {
      ...MD3DarkTheme.fonts.titleLarge,
      fontFamily: darkTheme.fonts.medium,
    },
    titleMedium: {
      ...MD3DarkTheme.fonts.titleMedium,
      fontFamily: darkTheme.fonts.medium,
    },
    titleSmall: {
      ...MD3DarkTheme.fonts.titleSmall,
      fontFamily: darkTheme.fonts.medium,
    },
    bodyLarge: {
      ...MD3DarkTheme.fonts.bodyLarge,
      fontFamily: darkTheme.fonts.regular,
    },
    bodyMedium: {
      ...MD3DarkTheme.fonts.bodyMedium,
      fontFamily: darkTheme.fonts.regular,
    },
    bodySmall: {
      ...MD3DarkTheme.fonts.bodySmall,
      fontFamily: darkTheme.fonts.regular,
    },
    labelLarge: {
      ...MD3DarkTheme.fonts.labelLarge,
      fontFamily: darkTheme.fonts.medium,
    },
    labelMedium: {
      ...MD3DarkTheme.fonts.labelMedium,
      fontFamily: darkTheme.fonts.medium,
    },
    labelSmall: {
      ...MD3DarkTheme.fonts.labelSmall,
      fontFamily: darkTheme.fonts.regular,
    },
  },
  // 使用现有的圆角配置
  roundness: darkTheme.radius.md,
};

// 导出类型
export type AppPaperTheme = typeof paperLightTheme;
