import React from 'react';
import { TouchableOpacity, Text } from 'react-native';
import { useAppTheme } from '@/hooks/useAppTheme';
import { createStyles } from './styles';
import { Sparkles } from 'lucide-react-native';

interface AiSuggestionCardProps {
  suggestion: string;
  onSelect: (text: string) => void;
}

export default function AiSuggestionCard({ 
  suggestion, 
  onSelect 
}: AiSuggestionCardProps) {
  const theme = useAppTheme();
  const styles = createStyles(theme);

  return (
    <TouchableOpacity 
      style={styles.container}
      onPress={() => onSelect(suggestion)}
    >
      <Sparkles size={16} color={theme.colors.primary} style={styles.icon} />
      <Text style={styles.suggestionText} numberOfLines={1}>
        {suggestion}
      </Text>
    </TouchableOpacity>
  );
}
