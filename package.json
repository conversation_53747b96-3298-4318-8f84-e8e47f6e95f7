{"name": "superpose-app", "main": "expo-router/entry", "version": "1.0.0", "private": true, "scripts": {"dev": "cross-env EXPO_NO_TELEMETRY=1 expo start", "build:web": "expo export --platform web", "lint": "expo lint", "start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "test": "jest --watchAll", "test-api-keys": "node scripts/test-api-keys.js", "test-api-keys-axios": "node scripts/test-api-keys-axios.js"}, "dependencies": {"@babel/runtime": "^7.27.1", "@expo-google-fonts/inter": "^0.2.3", "@expo-google-fonts/merriweather": "^0.2.3", "@expo/vector-icons": "^14.0.2", "@lucide/lab": "^0.1.2", "@react-native-async-storage/async-storage": "^2.1.2", "@react-native-community/netinfo": "^11.4.1", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/native": "^7.0.14", "@shopify/flash-list": "^1.7.6", "@supabase/supabase-js": "^2.49.4", "base64-arraybuffer": "^1.0.2", "date-fns": "^4.1.0", "expo": "53.0.9", "expo-blur": "^14.1.4", "expo-camera": "^16.1.6", "expo-constants": "^17.1.6", "expo-file-system": "^18.1.10", "expo-font": "^13.3.1", "expo-haptics": "^14.1.4", "expo-image-picker": "^16.1.4", "expo-linear-gradient": "^14.1.4", "expo-linking": "^7.1.4", "expo-localization": "~16.1.5", "expo-router": "5.0.7", "expo-splash-screen": "^0.30.8", "expo-status-bar": "^2.2.3", "expo-symbols": "^0.4.4", "expo-system-ui": "^5.0.7", "expo-web-browser": "^14.1.6", "i18n-js": "^4.5.1", "i18next": "^25.0.2", "lucide-react-native": "^0.509.0", "react": "19.0.0", "react-dom": "19.0.0", "react-i18next": "^15.5.1", "react-native": "0.79.2", "react-native-dotenv": "^3.4.11", "react-native-gesture-handler": "^2.24.0", "react-native-paper": "^5.14.5", "react-native-reanimated": "^3.17.5", "react-native-safe-area-context": "5.4.0", "react-native-screens": "^4.10.0", "react-native-svg": "^15.11.2", "react-native-url-polyfill": "^2.0.0", "react-native-web": "^0.20.0", "react-native-webview": "13.13.5", "zustand": "^5.0.4"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/plugin-transform-flow-strip-types": "^7.27.1", "@testing-library/react-native": "^13.2.0", "@types/jest": "^29.5.14", "@types/react": "~19.0.14", "axios": "^1.9.0", "babel-plugin-module-resolver": "^5.0.2", "cross-env": "^7.0.3", "dotenv": "^16.5.0", "jest": "^29.7.0", "jest-expo": "^53.0.5", "node-fetch": "^3.3.2", "react-test-renderer": "19.0.0", "ts-jest": "^29.3.4", "typescript": "^5.3.3"}, "packageManager": "pnpm@10.11.0+sha512.6540583f41cc5f628eb3d9773ecee802f4f9ef9923cc45b69890fb47991d4b092964694ec3a4f738a420c918a333062c8b925d312f42e4f0c263eb603551f977"}