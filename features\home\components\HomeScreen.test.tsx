import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import { PaperProvider } from 'react-native-paper';
import HomeScreen from '../screens/HomeScreen';
import { paperLightTheme } from '@/lib/theme/paperThemes';

// Mock the router
jest.mock('expo-router', () => ({
  useRouter: () => ({
    push: jest.fn(),
  }),
}));

// Mock the translation hook
jest.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string, defaultValue: string) => defaultValue,
  }),
}));

// Mock the home screen data hook
jest.mock('../hooks/useHomeScreenData', () => ({
  useHomeScreenData: () => ({
    storyListTabs: ['推荐', '热门', '最新', '关注'],
    activeStoryListTab: '推荐',
    setActiveStoryListTab: jest.fn(),
    stories: [],
    featuredStory: null,
    isLoading: false,
    error: null,
    fetchStories: jest.fn(),
    mapTabToFilter: jest.fn(),
  }),
}));

const renderWithTheme = (component: React.ReactElement) => {
  return render(
    <PaperProvider theme={paperLightTheme}>
      {component}
    </PaperProvider>
  );
};

describe('HomeScreen Paper Migration', () => {
  it('renders correctly with Paper components', () => {
    const { getByPlaceholderText, getByText } = renderWithTheme(
      <HomeScreen />
    );

    // Check if search bar is rendered (Paper TextInput)
    expect(getByPlaceholderText('搜索故事、作者、标签...')).toBeTruthy();
    
    // Check if theme section title is rendered (Paper Text)
    expect(getByText('故事主题')).toBeTruthy();
    
    // Check if story list tabs are rendered (Paper SegmentedButtons)
    expect(getByText('推荐')).toBeTruthy();
    expect(getByText('热门')).toBeTruthy();
    expect(getByText('最新')).toBeTruthy();
    expect(getByText('关注')).toBeTruthy();
  });

  it('handles search input correctly', () => {
    const { getByPlaceholderText } = renderWithTheme(
      <HomeScreen />
    );

    const searchInput = getByPlaceholderText('搜索故事、作者、标签...');
    fireEvent.changeText(searchInput, 'test search');
    
    expect(searchInput.props.value).toBe('test search');
  });

  it('renders loading state correctly', () => {
    // Mock loading state
    jest.doMock('../hooks/useHomeScreenData', () => ({
      useHomeScreenData: () => ({
        storyListTabs: ['推荐', '热门', '最新', '关注'],
        activeStoryListTab: '推荐',
        setActiveStoryListTab: jest.fn(),
        stories: [],
        featuredStory: null,
        isLoading: true,
        error: null,
        fetchStories: jest.fn(),
        mapTabToFilter: jest.fn(),
      }),
    }));

    const { getByTestId } = renderWithTheme(<HomeScreen />);
    
    // Paper ActivityIndicator should be rendered
    expect(getByTestId('activity-indicator')).toBeTruthy();
  });

  it('renders error state correctly', () => {
    // Mock error state
    jest.doMock('../hooks/useHomeScreenData', () => ({
      useHomeScreenData: () => ({
        storyListTabs: ['推荐', '热门', '最新', '关注'],
        activeStoryListTab: '推荐',
        setActiveStoryListTab: jest.fn(),
        stories: [],
        featuredStory: null,
        isLoading: false,
        error: 'Test error message',
        fetchStories: jest.fn(),
        mapTabToFilter: jest.fn(),
      }),
    }));

    const { getByText } = renderWithTheme(<HomeScreen />);
    
    // Paper Button for retry should be rendered
    expect(getByText('重试')).toBeTruthy();
  });
});
