import React from 'react';
import { View, Text } from 'react-native';
import { Crown } from 'lucide-react-native';
import { createStyles } from './styles';
import { useAppTheme } from '@/hooks/useAppTheme';

interface FeaturedStoryCardHeaderProps {
  title: string;
  isPremium: boolean;
}

export function FeaturedStoryCardHeader({
  title,
  isPremium,
}: FeaturedStoryCardHeaderProps) {
  const appTheme = useAppTheme();
  const styles = createStyles(appTheme);

  return (
    <>
      {isPremium && (
        <View style={styles.premiumBadge}>
          <Crown size={12} color="#000" />
          <Text style={styles.premiumText}>会员</Text>
        </View>
      )}
      <Text style={styles.title}>{title}</Text>
    </>
  );
}
