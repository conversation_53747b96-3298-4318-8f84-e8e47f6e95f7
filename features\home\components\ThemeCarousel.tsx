import React from 'react';
import { View, ScrollView } from 'react-native';
import { useAppTheme } from '@/hooks/useAppTheme';
import { createStyles } from './ThemeCarousel.styles';
import ThemeCard, { Theme } from '@/components/stories/ThemeCard'; // Use existing card

interface ThemeCarouselProps {
  themes: Theme[];
  onThemePress?: (themeId: string) => void;
}

export function ThemeCarousel({ themes, onThemePress }: ThemeCarouselProps) {
  const theme = useAppTheme();
  const styles = createStyles(theme);

  return (
    <View style={styles.container}>
      <ScrollView 
        horizontal 
        showsHorizontalScrollIndicator={false} 
        contentContainerStyle={styles.scrollViewContent}
      >
        {themes.map(themeItem => (
          <ThemeCard 
            key={themeItem.id} 
            theme={themeItem} 
            onPress={() => onThemePress?.(themeItem.id)} 
          />
        ))}
      </ScrollView>
    </View>
  );
} 